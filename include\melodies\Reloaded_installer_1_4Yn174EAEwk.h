// This file is auto-generated by preprocess_audio_files.py. Do not edit this manually.
#pragma once

#include <Arduino.h>

/*
Converted from Reloaded installer #1 [4Yn174EAEwk].mp3 - 922 notes, entire file
Note duration: 0.1s per note
Max duration: entire files
*/

// Forward declaration of beep function
void beep(int frequency, int duration);

constexpr int Reloaded_installer_1_4Yn174EAEwk_note_count = 922;
constexpr int Reloaded_installer_1_4Yn174EAEwk_frequencies[] = {
    0, 857, 852, 860, 869, 847, 285, 854, 
    846, 869, 853, 213, 853, 853, 427, 763, 
    679, 765, 212, 638, 650, 637, 508, 513, 
    504, 514, 505, 517, 539, 571, 570, 573, 
    569, 567, 671, 679, 697, 664, 212, 212, 
    640, 150, 570, 642, 203, 503, 511, 507, 
    428, 508, 509, 428, 428, 571, 224, 573, 
    576, 571, 572, 168, 857, 847, 858, 865, 
    224, 226, 907, 859, 848, 224, 223, 763, 
    764, 169, 680, 765, 224, 680, 764, 251, 
    509, 513, 639, 645, 636, 679, 678, 679, 
    251, 251, 670, 251, 763, 907, 900, 902, 
    857, 848, 858, 765, 763, 855, 250, 251, 
    563, 578, 569, 212, 569, 284, 860, 678, 
    680, 680, 675, 282, 681, 684, 679, 678, 
    282, 643, 637, 213, 639, 508, 643, 282, 
    510, 510, 281, 571, 571, 567, 572, 570, 
    673, 687, 677, 282, 282, 671, 284, 642, 
    213, 643, 213, 504, 515, 504, 430, 429, 
    282, 149, 282, 222, 571, 572, 572, 571, 
    572, 569, 169, 853, 868, 847, 225, 225, 
    907, 906, 855, 225, 853, 763, 763, 764, 
    680, 680, 765, 679, 763, 509, 509, 511, 
    641, 634, 189, 681, 679, 672, 250, 251, 
    251, 251, 250, 764, 896, 918, 906, 848, 
    249, 852, 764, 251, 857, 250, 210, 570, 
    282, 573, 572, 282, 571, 680, 282, 283, 
    573, 580, 281, 570, 571, 281, 580, 569, 
    282, 283, 212, 283, 282, 211, 282, 282, 
    571, 569, 282, 572, 578, 282, 211, 680, 
    282, 571, 763, 282, 281, 847, 862, 282, 
    853, 865, 281, 863, 217, 282, 212, 214, 
    282, 286, 453, 225, 225, 452, 224, 453, 
    452, 225, 224, 575, 571, 224, 863, 859, 
    224, 226, 848, 223, 224, 859, 222, 225, 
    168, 225, 225, 504, 516, 252, 513, 508, 
    252, 249, 643, 251, 251, 639, 251, 251, 
    849, 250, 251, 642, 642, 250, 763, 763, 
    250, 248, 642, 251, 251, 642, 282, 571, 
    679, 678, 283, 642, 641, 283, 509, 509, 
    281, 509, 572, 282, 578, 568, 580, 283, 
    570, 572, 282, 427, 568, 282, 211, 571, 
    282, 573, 680, 282, 570, 640, 574, 282, 
    764, 763, 282, 211, 858, 281, 860, 854, 
    282, 867, 847, 284, 281, 857, 282, 282, 
    340, 223, 225, 167, 338, 224, 452, 508, 
    225, 224, 453, 224, 453, 333, 223, 225, 
    338, 344, 225, 168, 228, 224, 344, 167, 
    224, 766, 255, 188, 252, 257, 254, 251, 
    429, 431, 252, 428, 429, 378, 255, 256, 
    253, 257, 253, 248, 251, 677, 251, 251, 
    579, 567, 251, 571, 571, 282, 678, 572, 
    282, 281, 213, 282, 282, 572, 282, 282, 
    426, 282, 282, 339, 280, 282, 287, 215, 
    281, 281, 282, 282, 281, 571, 282, 282, 
    679, 281, 282, 210, 277, 282, 430, 571, 
    282, 279, 210, 283, 282, 508, 284, 282, 
    567, 283, 282, 211, 217, 282, 571, 572, 
    225, 676, 572, 224, 225, 170, 225, 224, 
    429, 225, 224, 453, 510, 224, 384, 429, 
    224, 343, 333, 224, 224, 334, 225, 225, 
    508, 507, 251, 679, 679, 252, 252, 509, 
    252, 571, 429, 252, 248, 384, 251, 252, 
    508, 509, 251, 764, 764, 250, 764, 642, 
    571, 569, 570, 282, 281, 678, 284, 282, 
    212, 216, 283, 428, 570, 281, 427, 508, 
    282, 339, 339, 283, 284, 283, 284, 282, 
    284, 277, 282, 211, 571, 282, 680, 213, 
    282, 210, 211, 282, 281, 428, 281, 282, 
    210, 282, 281, 508, 216, 281, 573, 570, 
    282, 212, 568, 282, 282, 572, 224, 224, 
    678, 334, 224, 170, 227, 225, 429, 571, 
    224, 228, 509, 223, 225, 442, 222, 225, 
    334, 340, 225, 341, 225, 224, 508, 508, 
    252, 250, 679, 252, 250, 510, 251, 252, 
    508, 189, 252, 429, 381, 428, 435, 509, 
    429, 856, 764, 680, 679, 643, 252, 571, 
    425, 431, 428, 429, 431, 427, 428, 430, 
    429, 429, 427, 430, 427, 427, 209, 428, 
    508, 510, 524, 569, 571, 569, 572, 680, 
    676, 679, 427, 428, 429, 428, 429, 427, 
    424, 430, 428, 429, 429, 212, 428, 429, 
    427, 423, 508, 517, 540, 582, 517, 513, 
    504, 149, 507, 223, 225, 340, 340, 337, 
    284, 339, 339, 341, 169, 338, 224, 225, 
    224, 332, 339, 225, 453, 476, 169, 571, 
    571, 223, 224, 224, 511, 515, 381, 383, 
    379, 319, 189, 190, 383, 379, 250, 252, 
    251, 250, 251, 192, 189, 428, 381, 513, 
    503, 517, 508, 252, 249, 250, 428, 431, 
    428, 429, 427, 430, 422, 426, 430, 429, 
    428, 430, 428, 427, 428, 425, 508, 509, 
    518, 542, 571, 571, 509, 680, 679, 679, 
    679, 431, 429, 429, 429, 428, 426, 429, 
    428, 429, 428, 429, 215, 429, 430, 428, 
    508, 509, 522, 566, 571, 505, 514, 507, 
    502, 505, 225, 339, 341, 336, 454, 453, 
    338, 342, 168, 340, 225, 224, 224, 336, 
    339, 224, 453, 466, 498, 570, 571, 509, 
    225, 225, 569, 572, 251, 504, 514, 507, 
    190, 518, 505, 518, 509, 251, 251, 502, 
    252, 193, 189, 183, 173, 161, 151, 0, 
    0, 0, 153, 0, 863, 847, 860, 854, 
    853, 338, 284, 846, 862, 844, 858, 853, 
    853, 428, 763, 679, 679, 211, 643, 646, 
    213, 638, 508, 508, 507, 504, 514, 530, 
    571, 571, 569, 572, 564, 663, 696, 667, 
    667, 212, 212, 641, 643, 213, 643, 212, 
    504, 515, 506, 429, 428, 509, 147, 428, 
    570, 571, 573, 572, 570, 572, 169, 857, 
    849, 865, 168, 224, 226, 907, 856, 854, 
    224, 225, 763, 763, 763, 679, 765, 224, 
    680, 763, 509, 508, 511, 642, 636, 189, 
    679, 679, 673, 252, 251, 250, 251, 763, 
    190, 896, 189, 857, 252, 0, 859, 763, 
    250, 245
};
constexpr int Reloaded_installer_1_4Yn174EAEwk_durations[] = {
    100, 100, 100, 100, 100, 100, 100, 100, 
    100, 100, 100, 100, 100, 100, 100, 100, 
    100, 100, 100, 100, 100, 100, 100, 100, 
    100, 100, 100, 100, 100, 100, 100, 100, 
    100, 100, 100, 100, 100, 100, 100, 100, 
    100, 100, 100, 100, 100, 100, 100, 100, 
    100, 100, 100, 100, 100, 100, 100, 100, 
    100, 100, 100, 100, 100, 100, 100, 100, 
    100, 100, 100, 100, 100, 100, 100, 100, 
    100, 100, 100, 100, 100, 100, 100, 100, 
    100, 100, 100, 100, 100, 100, 100, 100, 
    100, 100, 100, 100, 100, 100, 100, 100, 
    100, 100, 100, 100, 100, 100, 100, 100, 
    100, 100, 100, 100, 100, 100, 100, 100, 
    100, 100, 100, 100, 100, 100, 100, 100, 
    100, 100, 100, 100, 100, 100, 100, 100, 
    100, 100, 100, 100, 100, 100, 100, 100, 
    100, 100, 100, 100, 100, 100, 100, 100, 
    100, 100, 100, 100, 100, 100, 100, 100, 
    100, 100, 100, 100, 100, 100, 100, 100, 
    100, 100, 100, 100, 100, 100, 100, 100, 
    100, 100, 100, 100, 100, 100, 100, 100, 
    100, 100, 100, 100, 100, 100, 100, 100, 
    100, 100, 100, 100, 100, 100, 100, 100, 
    100, 100, 100, 100, 100, 100, 100, 100, 
    100, 100, 100, 100, 100, 100, 100, 100, 
    100, 100, 100, 100, 100, 100, 100, 100, 
    100, 100, 100, 100, 100, 100, 100, 100, 
    100, 100, 100, 100, 100, 100, 100, 100, 
    100, 100, 100, 100, 100, 100, 100, 100, 
    100, 100, 100, 100, 100, 100, 100, 100, 
    100, 100, 100, 100, 100, 100, 100, 100, 
    100, 100, 100, 100, 100, 100, 100, 100, 
    100, 100, 100, 100, 100, 100, 100, 100, 
    100, 100, 100, 100, 100, 100, 100, 100, 
    100, 100, 100, 100, 100, 100, 100, 100, 
    100, 100, 100, 100, 100, 100, 100, 100, 
    100, 100, 100, 100, 100, 100, 100, 100, 
    100, 100, 100, 100, 100, 100, 100, 100, 
    100, 100, 100, 100, 100, 100, 100, 100, 
    100, 100, 100, 100, 100, 100, 100, 100, 
    100, 100, 100, 100, 100, 100, 100, 100, 
    100, 100, 100, 100, 100, 100, 100, 100, 
    100, 100, 100, 100, 100, 100, 100, 100, 
    100, 100, 100, 100, 100, 100, 100, 100, 
    100, 100, 100, 100, 100, 100, 100, 100, 
    100, 100, 100, 100, 100, 100, 100, 100, 
    100, 100, 100, 100, 100, 100, 100, 100, 
    100, 100, 100, 100, 100, 100, 100, 100, 
    100, 100, 100, 100, 100, 100, 100, 100, 
    100, 100, 100, 100, 100, 100, 100, 100, 
    100, 100, 100, 100, 100, 100, 100, 100, 
    100, 100, 100, 100, 100, 100, 100, 100, 
    100, 100, 100, 100, 100, 100, 100, 100, 
    100, 100, 100, 100, 100, 100, 100, 100, 
    100, 100, 100, 100, 100, 100, 100, 100, 
    100, 100, 100, 100, 100, 100, 100, 100, 
    100, 100, 100, 100, 100, 100, 100, 100, 
    100, 100, 100, 100, 100, 100, 100, 100, 
    100, 100, 100, 100, 100, 100, 100, 100, 
    100, 100, 100, 100, 100, 100, 100, 100, 
    100, 100, 100, 100, 100, 100, 100, 100, 
    100, 100, 100, 100, 100, 100, 100, 100, 
    100, 100, 100, 100, 100, 100, 100, 100, 
    100, 100, 100, 100, 100, 100, 100, 100, 
    100, 100, 100, 100, 100, 100, 100, 100, 
    100, 100, 100, 100, 100, 100, 100, 100, 
    100, 100, 100, 100, 100, 100, 100, 100, 
    100, 100, 100, 100, 100, 100, 100, 100, 
    100, 100, 100, 100, 100, 100, 100, 100, 
    100, 100, 100, 100, 100, 100, 100, 100, 
    100, 100, 100, 100, 100, 100, 100, 100, 
    100, 100, 100, 100, 100, 100, 100, 100, 
    100, 100, 100, 100, 100, 100, 100, 100, 
    100, 100, 100, 100, 100, 100, 100, 100, 
    100, 100, 100, 100, 100, 100, 100, 100, 
    100, 100, 100, 100, 100, 100, 100, 100, 
    100, 100, 100, 100, 100, 100, 100, 100, 
    100, 100, 100, 100, 100, 100, 100, 100, 
    100, 100, 100, 100, 100, 100, 100, 100, 
    100, 100, 100, 100, 100, 100, 100, 100, 
    100, 100, 100, 100, 100, 100, 100, 100, 
    100, 100, 100, 100, 100, 100, 100, 100, 
    100, 100, 100, 100, 100, 100, 100, 100, 
    100, 100, 100, 100, 100, 100, 100, 100, 
    100, 100, 100, 100, 100, 100, 100, 100, 
    100, 100, 100, 100, 100, 100, 100, 100, 
    100, 100, 100, 100, 100, 100, 100, 100, 
    100, 100, 100, 100, 100, 100, 100, 100, 
    100, 100, 100, 100, 100, 100, 100, 100, 
    100, 100, 100, 100, 100, 100, 100, 100, 
    100, 100, 100, 100, 100, 100, 100, 100, 
    100, 100, 100, 100, 100, 100, 100, 100, 
    100, 100, 100, 100, 100, 100, 100, 100, 
    100, 100, 100, 100, 100, 100, 100, 100, 
    100, 100, 100, 100, 100, 100, 100, 100, 
    100, 100, 100, 100, 100, 100, 100, 100, 
    100, 100, 100, 100, 100, 100, 100, 100, 
    100, 100, 100, 100, 100, 100, 100, 100, 
    100, 100, 100, 100, 100, 100, 100, 100, 
    100, 100, 100, 100, 100, 100, 100, 100, 
    100, 100, 100, 100, 100, 100, 100, 100, 
    100, 100, 100, 100, 100, 100, 100, 100, 
    100, 100, 100, 100, 100, 100, 100, 100, 
    100, 100, 100, 100, 100, 100, 100, 100, 
    100, 100, 100, 100, 100, 100, 100, 100, 
    100, 100, 100, 100, 100, 100, 100, 100, 
    100, 100, 100, 100, 100, 100, 100, 100, 
    100, 100, 100, 100, 100, 100, 100, 100, 
    100, 100, 100, 100, 100, 100, 100, 100, 
    100, 100, 100, 100, 100, 100, 100, 100, 
    100, 100, 100, 100, 100, 100, 100, 100, 
    100, 100, 100, 100, 100, 100, 100, 100, 
    100, 100, 100, 100, 100, 100, 100, 100, 
    100, 100, 100, 100, 100, 100, 100, 100, 
    100, 100
};

// Helper function to play this melody
inline void play_Reloaded_installer_1_4Yn174EAEwk() {
    for (int i = 0; i < Reloaded_installer_1_4Yn174EAEwk_note_count; i++) {
        if (Reloaded_installer_1_4Yn174EAEwk_frequencies[i] > 0) {
            beep(Reloaded_installer_1_4Yn174EAEwk_frequencies[i], Reloaded_installer_1_4Yn174EAEwk_durations[i]);
        } else {
            delay(Reloaded_installer_1_4Yn174EAEwk_durations[i]); // Rest (silence)
        }
    }
}
