#include "my_epaper.h"
#include "images/image_bitmaps.h"
#include "my_io.h"

// Libraries for EPaper
#include <SPI.h>
#include <math.h>

#define ENABLE_GxEPD2_GFX 1
#include <GxEPD2_4G_4G.h>
#include <U8g2_for_Adafruit_GFX.h>

// 2.7inch EPD (Electronic Paper Display)
// 264 × 176 Pixels
// WF10190CZ22

#define EPD_SDIN                                                               \
  27 // the data line from the master to the slave in SPI communication.
#define EPD_SCLK 5 // the SPI communication clock.

#define EPD_CS 22 // slave chip select, when CS is low, the chip is enabled
#define EPD_DC                                                                 \
  2 // data/command control pin, when DC = 0, write command, when DC = 1, write
    // data.
#define EPD_RST 23
#define EPD_BUSY 35

#define MAX_DISPLAY_BUFFER_SIZE 800
#define MAX_HEIGHT(EPD)                                                        \
  (EPD::HEIGHT <= MAX_DISPLAY_BUFFER_SIZE / (EPD::WIDTH / 4)                   \
       ? EPD::HEIGHT                                                           \
       : MAX_DISPLAY_BUFFER_SIZE / (EPD::WIDTH / 4))
GxEPD2_4G_4G<GxEPD2_270, MAX_HEIGHT(GxEPD2_270)>
    EDisplay(GxEPD2_270(EPD_CS, EPD_DC, EPD_RST, EPD_BUSY));
U8G2_FOR_ADAFRUIT_GFX EDiplayFont;

void DisplayBatteryStatus(uint16_t color) {
  int16_t batteryLevel = batteryVoltage();

  if (batteryLevel < 3.2) {
  }
}

void EPDSetup() {
  EDisplay.init();
  EDisplay.setRotation(3); // 0--> No rotation ,  1--> rotate 90 deg

  EDiplayFont.begin(EDisplay); // connect u8g2 procedures to Adafruit GFX
  EDiplayFont.setFontMode(0); // 0: transparent, 1: solid
  EDiplayFont.setFontDirection(0); // left to right (this is default)
  EDiplayFont.setForegroundColor(GxEPD_BLACK); // apply Adafruit GFX color
  EDiplayFont.setBackgroundColor(GxEPD_WHITE); // apply Adafruit GFX color
  EDiplayFont.setFont(
      u8g2_font_helvR08_tf); // https://github.com/olikraus/u8g2/wiki/fntlistall
}

void PrintBatteryVoltage() {
  String text = "Volts:" + String(batteryVoltage()) + " V";

  // Calculate text dimensions and position
  int textLength = text.length();
  int charWidth = 6;  // Approximate character width for u8g2_font_helvR08_tf
  int charHeight = 8; // Approximate character height for u8g2_font_helvR08_tf
  int textWidth = textLength * charWidth;
  int textHeight = charHeight;

  // Position text in bottom-right corner with some padding
  int padding = 4;
  int textX = EDisplay.width() - textWidth - padding;
  int textY = EDisplay.height() - padding;

  // Draw background rectangle with some extra padding
  int rectPadding = 2;
  int rectX = textX - rectPadding;
  int rectY = textY - textHeight - rectPadding;
  int rectWidth = textWidth + (rectPadding * 2);
  int rectHeight = textHeight + (rectPadding * 2);

  EDisplay.fillRect(rectX, rectY, rectWidth, rectHeight, GxEPD_WHITE);

  // Set text position and print
  EDiplayFont.setCursor(textX, textY);
  EDiplayFont.setForegroundColor(255); // Black text for good contrast
  EDiplayFont.print(text);

  // Reset text color to default
  EDiplayFont.setForegroundColor(GxEPD_BLACK);
}

void DrawBatteryState() {
  float voltage = batteryVoltage();
  if (batteryEmpty(voltage)) {
    BaseImage batteryImage(IMAGE_BATTERY_EMPTY);
    batteryImage.draw(EDisplay, 0, 0);
  } else if (batteryLow(voltage)) {
    BaseImage batteryImage(IMAGE_BATTERY_LOW);
    batteryImage.draw(EDisplay, 0, 0);
  } else if (batteryMedium(voltage)) {
    BaseImage batteryImage(IMAGE_BATTERY_MEDIUM);
    batteryImage.draw(EDisplay, 0, 0);
  } else if (batteryHigh(voltage)) {
    BaseImage batteryImage(IMAGE_BATTERY_HIGH);
    batteryImage.draw(EDisplay, 0, 0);
  } else if (batteryFull(voltage)) {
    BaseImage batteryImage(IMAGE_BATTERY_FULL);
    batteryImage.draw(EDisplay, 0, 0);
  }
}

void DrawTestingXYCross(int posX, int posY, int color, int size) {
  EDisplay.drawLine(posX, posY, posX + size, posY, color);
  EDisplay.drawLine(posX, posY, posX, posY + size, color);

  EDiplayFont.setCursor(posX + 5, posY + 20);
  EDisplay.drawPixel(posX + 5, posY + 20, color);
  EDiplayFont.print("C");

  EDiplayFont.setCursor(posX + size, posY + 10);
  EDisplay.drawPixel(posX + size, posY + 10, color);
  EDiplayFont.print("X+");

  EDiplayFont.setCursor(posX - 5, posY + size);
  EDisplay.drawPixel(posX - 5, posY + size, color);
  EDiplayFont.print("Y+");
}

// Determine which background image to display based on boot count
ImageId getCurrentBackgroundImage() {
  int bootCount = getBootCount();

  if (bootCount % 2 == 1) {
    Serial.println("Displaying background image (boot count: " + String(bootCount) + ")");
    return IMAGE_BACKGROUND;
  } else {
    Serial.println("Displaying interior image (boot count: " + String(bootCount) + ")");
    return IMAGE_INTERIER;
  }
}

void EPDUpdate() {
  EDisplay.fillScreen(GxEPD_WHITE);
  EDisplay.refresh();

  // Use cycling background image based on boot count
  ImageId currentImage = getCurrentBackgroundImage();
  BaseImage background(currentImage);
  Serial.println("Current image: " + String(currentImage));

  EDisplay.firstPage();
  do {
    background.draw(EDisplay, 0, 0);

    DrawBatteryState();
    PrintBatteryVoltage();
  } while (EDisplay.nextPage());
}
