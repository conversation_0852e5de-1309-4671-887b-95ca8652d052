#include "my_oled.h"
#include "my_io.h"

//Libraries for OLED Display
#include <Wire.h>
#include <Adafruit_GFX.h>
#include <Adafruit_SSD1306.h>

//OLED pins
#define OLED_SDA 4
#define OLED_SCL 15
#define OLED_RST 16

#define SCREEN_WIDTH 128  // OLED display width,  in pixels
#define SCREEN_HEIGHT 64  // OLED display height, in pixels

Adafruit_SSD1306 ODisplay(SCREEN_WIDTH, SCREEN_HEIGHT, &Wire, OLED_RST);

void OLEDSetup() {
  pinMode(OLED_RST, OUTPUT);

  // Initialize I2C before reset sequence
  Wire.begin(OLED_SDA, OLED_SCL);

  // Reset OLED display with proper timing
  digitalWrite(OLED_RST, LOW);
  delay(20);  // Increased delay for proper reset
  digitalWrite(OLED_RST, HIGH);
  delay(20);  // Allow display to stabilize after reset

  // Initialize OLED
  if (!ODisplay.begin(SSD1306_SWITCHCAPVCC, 0x3c, false, false)) {  // Address 0x3C for 128x64
    Serial.println(F("SSD1306 allocation failed"));
    error();
  }

  // Clear any garbage data from display buffer
  ODisplay.clearDisplay();
  ODisplay.display();  // Push clear buffer to display

  Serial.println(F("OLED initialized successfully"));
}

void OLEDUpdate() {
  ODisplay.clearDisplay();
  ODisplay.setTextColor(WHITE);
  ODisplay.setTextSize(1);
  ODisplay.setCursor(0, 0);
  ODisplay.println(String(batteryVoltage()) + " V");
  ODisplay.display();
}
