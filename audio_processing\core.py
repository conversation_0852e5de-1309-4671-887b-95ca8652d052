"""
Core audio processing functions for converting MP3 files to ESP32 buzzer format.
"""

import os
import numpy as np
from pathlib import Path
from typing import Op<PERSON>, List, Tuple
import librosa


def get_script_dir() -> Path:
    """Get the directory of this script, fallback to cwd if __file__ is not defined."""
    try:
        return Path(__file__).parent
    except NameError:
        return Path(os.getcwd())


def get_template_path(template_name: str) -> Path:
    """Get the path to a template file, handling different execution contexts."""
    script_dir = get_script_dir()

    # First try: template in same directory as script (normal case)
    template_path = script_dir / template_name
    if template_path.exists():
        return template_path

    # Second try: template in audio_processing subdirectory (if running from root)
    template_path = script_dir / "audio_processing" / template_name
    if template_path.exists():
        return template_path

    # Third try: look in current working directory
    template_path = Path(os.getcwd()) / "audio_processing" / template_name
    if template_path.exists():
        return template_path

    # Fallback: original path (will cause error if not found)
    return script_dir / template_name


def load_cpp_template() -> str:
    """Load the C++ melody template."""
    template_path = get_template_path("cpp_melody_template.txt")
    with open(template_path, "r") as f:
        return f.read()


def load_master_template() -> str:
    """Load the C++ master header template."""
    template_path = get_template_path("cpp_master_header_template.txt")
    with open(template_path, "r") as f:
        return f.read()


def frequency_to_note(frequency: float) -> Tuple[str, int]:
    """
    Convert frequency to musical note name and octave.
    
    Args:
        frequency: Frequency in Hz
        
    Returns:
        tuple: (note_name, octave) e.g., ("A", 4) for A4 (440 Hz)
    """
    if frequency <= 0:
        return ("REST", 0)
    
    # A4 = 440 Hz is our reference
    A4 = 440.0
    note_names = ["C", "C#", "D", "D#", "E", "F", "F#", "G", "G#", "A", "A#", "B"]
    
    # Calculate the number of semitones from A4
    semitones_from_a4 = 12 * np.log2(frequency / A4)
    
    # Round to nearest semitone
    semitone_index = round(semitones_from_a4)
    
    # Calculate octave (A4 is in octave 4)
    octave = 4 + (semitone_index + 9) // 12  # +9 because A is the 9th note in the chromatic scale starting from C
    
    # Calculate note index
    note_index = (semitone_index + 9) % 12
    
    return (note_names[note_index], octave)


def note_to_frequency(note: str, octave: int) -> float:
    """
    Convert musical note to frequency.
    
    Args:
        note: Note name (C, C#, D, etc.)
        octave: Octave number
        
    Returns:
        Frequency in Hz
    """
    if note == "REST":
        return 0.0
    
    # A4 = 440 Hz is our reference
    A4 = 440.0
    note_names = ["C", "C#", "D", "D#", "E", "F", "F#", "G", "G#", "A", "A#", "B"]
    
    if note not in note_names:
        return 0.0
    
    # Calculate semitones from A4
    note_index = note_names.index(note)
    semitones_from_a4 = (octave - 4) * 12 + (note_index - 9)  # -9 because A is the 9th note
    
    # Calculate frequency
    frequency = A4 * (2 ** (semitones_from_a4 / 12))
    
    return frequency


def extract_melody_from_audio(file_path: Path, max_duration: Optional[float] = None,
                            note_duration: float = 0.2) -> List[Tuple[float, float]]:
    """
    Extract a simplified melody from an MP3 file.

    Args:
        file_path: Path to MP3 file
        max_duration: Maximum duration to process (seconds), None for entire file
        note_duration: Duration of each note segment (seconds)

    Returns:
        List of (frequency, duration) tuples representing the melody
    """
    print(f"Loading audio file: {file_path}")
    
    # Load audio file
    y, sr = librosa.load(file_path, duration=max_duration)
    
    # Extract pitch using harmonic-percussive separation and pitch tracking
    y_harmonic, y_percussive = librosa.effects.hpss(y)
    
    # Use the harmonic component for pitch detection
    pitches, magnitudes = librosa.piptrack(y=y_harmonic, sr=sr, threshold=0.1)
    
    # Calculate hop length for the desired note duration
    hop_length = int(note_duration * sr)
    
    melody = []
    
    # Process audio in chunks
    for i in range(0, len(y), hop_length):
        chunk_end = min(i + hop_length, len(y))
        chunk = y_harmonic[i:chunk_end]
        
        if len(chunk) == 0:
            continue
            
        # Get the time frame index for this chunk
        frame_idx = i // 512  # Default hop_length for piptrack is 512
        
        if frame_idx < pitches.shape[1]:
            # Find the strongest pitch in this frame
            frame_pitches = pitches[:, frame_idx]
            frame_magnitudes = magnitudes[:, frame_idx]
            
            # Get the pitch with highest magnitude
            if len(frame_magnitudes) > 0 and np.max(frame_magnitudes) > 0.1:
                max_mag_idx = np.argmax(frame_magnitudes)
                frequency = frame_pitches[max_mag_idx]
                
                if frequency > 0:
                    # Clamp frequency to buzzer-friendly range (100-5000 Hz)
                    frequency = max(100, min(5000, frequency))
                    melody.append((frequency, note_duration))
                else:
                    melody.append((0.0, note_duration))  # Rest
            else:
                melody.append((0.0, note_duration))  # Rest
        else:
            melody.append((0.0, note_duration))  # Rest
    
    return melody


def simplify_melody(melody: List[Tuple[float, float]], max_notes: Optional[int] = None) -> List[Tuple[float, float]]:
    """
    Simplify melody by reducing the number of notes and quantizing to common frequencies.

    Args:
        melody: List of (frequency, duration) tuples
        max_notes: Maximum number of notes in output, None for no limit

    Returns:
        Simplified melody
    """
    if max_notes is None or len(melody) <= max_notes:
        return melody
    
    # Downsample by taking every nth note
    step = len(melody) // max_notes
    simplified = []
    
    for i in range(0, len(melody), step):
        if len(simplified) >= max_notes:
            break
        simplified.append(melody[i])
    
    return simplified


def melody_to_cpp_arrays(melody: List[Tuple[float, float]], array_name: str) -> Tuple[str, str, int]:
    """
    Convert melody to C++ arrays.
    
    Args:
        melody: List of (frequency, duration) tuples
        array_name: Name for the C++ arrays
        
    Returns:
        tuple: (frequencies_array, durations_array, note_count)
    """
    frequencies = []
    durations = []
    
    for freq, dur in melody:
        frequencies.append(int(freq))
        durations.append(int(dur * 1000))  # Convert to milliseconds
    
    # Format as C++ arrays
    freq_str = "{\n    "
    dur_str = "{\n    "
    
    for i, (freq, dur) in enumerate(zip(frequencies, durations)):
        freq_str += f"{freq}"
        dur_str += f"{dur}"
        
        if i < len(frequencies) - 1:
            freq_str += ", "
            dur_str += ", "
            
        if (i + 1) % 8 == 0:  # New line every 8 values
            freq_str += "\n    "
            dur_str += "\n    "
    
    freq_str += "\n}"
    dur_str += "\n}"
    
    return freq_str, dur_str, len(melody)


def clean_destination_folder(dst_folder: Path, src_folder: Path):
    """Clean up destination folder and preview files."""
    print(f"Cleaning up destination folder: {dst_folder}")

    # Clean up header files with error handling
    for header_file in dst_folder.glob("*.h"):
        try:
            header_file.unlink()
            print(f"Removed: {header_file}")
        except PermissionError:
            print(f"Warning: Could not remove {header_file} (permission denied)")
        except Exception as e:
            print(f"Warning: Could not remove {header_file}: {e}")

    print("Destination folder cleaned successfully")


def process_audio_file(input_path: Path, output_path: Path, max_duration: Optional[float] = None,
                      note_duration: float = 0.2, max_notes: Optional[int] = None) -> str:
    """
    Process audio file and generate C++ header.

    Args:
        input_path: Path to MP3 file
        output_path: Path for output header file
        max_duration: Maximum duration to process (seconds), None for entire file
        note_duration: Duration of each note segment (seconds)
        max_notes: Maximum number of notes in output, None for no limit

    Returns:
        Description of the processed melody
    """
    try:
        # Extract melody from audio
        melody = extract_melody_from_audio(input_path, max_duration, note_duration)
        
        if not melody:
            print(f"\t{input_path.name}: No melody could be extracted!")
            return "empty melody"
        
        # Simplify melody
        simplified_melody = simplify_melody(melody, max_notes)
        
        # Convert to C++ arrays
        array_name = output_path.stem.replace("-", "_").replace(" ", "_").replace("#", "").replace("[", "").replace("]", "")
        frequencies_array, durations_array, note_count = melody_to_cpp_arrays(simplified_melody, array_name)
        
        # Load template and generate header
        cpp_template = load_cpp_template()
        # Format duration info for template
        duration_info = "entire file" if max_duration is None else f"{max_duration}s max duration"
        max_duration_str = "entire file" if max_duration is None else str(max_duration)

        header_content = cpp_template.format(
            array_name=array_name,
            note_count=note_count,
            frequencies_array=frequencies_array,
            durations_array=durations_array,
            meta_data=f"Converted from {input_path.name} - {note_count} notes, {duration_info}",
            max_duration=max_duration_str,
            note_duration=note_duration
        )
        
        # Write header file
        with open(output_path.with_suffix(".h"), "w") as f:
            f.write(header_content)
        
        print(f"\t{input_path.name}: Extracted {note_count} notes, exported as {output_path.with_suffix('.h').name}")
        
        return f"melody with {note_count} notes"
        
    except Exception as e:
        print(f"\t{input_path.name}: Error processing audio - {str(e)}")
        return "error"
