; PlatformIO Project Configuration File
;
;   Build options: build flags, source filter
;   Upload options: custom upload port, speed and extra flags
;   Library options: dependencies, extra library storages
;   Advanced options: extra scripting
;
; Please visit documentation for the other options and examples
; https://docs.platformio.org/page/projectconf.html

[env:ttgo-lora32-v2_dosimeter]
platform = espressif32
board = ttgo-lora32-v1
framework = arduino
lib_deps =
	ameltech/SmartEverything NFC NT3H1101@^1.1.1
	olikraus/U8g2@^2.36.5
	olikraus/U8g2_for_Adafruit_GFX@^1.8.0
	adafruit/Adafruit SSD1306@^2.5.13
	sandeepmistry/LoRa@^0.8.0
	esphome/ESPAsyncWebServer-esphome@^3.3.0
	https://github.com/ZinggJM/GxEPD2_4G.git
extra_scripts = extra_script.py
