// This file is auto-generated by preprocess_audio_files.py. Do not edit this manually.
#pragma once

#include <Arduino.h>

/*
{meta_data}
Note duration: {note_duration}s per note
Max duration: {max_duration}s
*/

// Forward declaration of beep function
void beep(int frequency, int duration);

constexpr int {array_name}_note_count = {note_count};
constexpr int {array_name}_frequencies[] = {frequencies_array};
constexpr int {array_name}_durations[] = {durations_array};

// Helper function to play this melody
inline void play_{array_name}() {{
    for (int i = 0; i < {array_name}_note_count; i++) {{
        if ({array_name}_frequencies[i] > 0) {{
            beep({array_name}_frequencies[i], {array_name}_durations[i]);
        }} else {{
            delay({array_name}_durations[i]); // Rest (silence)
        }}
    }}
}}
