#!/usr/bin/env python3
"""Create a test black and white image for testing the new BW image format."""

from PIL import Image, ImageDraw, ImageFont
import os

def create_bw_test_image():
    # Create an image matching the epaper display size
    width, height = 264, 176
    img = Image.new('RGBA', (width, height), (255, 255, 255, 255))  # White background
    draw = ImageDraw.Draw(img)
    
    # Define pure black and white colors
    white = (255, 255, 255, 255)
    black = (0, 0, 0, 255)
    
    # Calculate center positions
    center_x = width // 2
    center_y = height // 2
    
    # Draw some simple black and white shapes centered on screen
    # Rectangle
    rect_size = 40
    draw.rectangle([
        center_x - 60 - rect_size//2, center_y - rect_size//2, 
        center_x - 60 + rect_size//2, center_y + rect_size//2
    ], fill=black, outline=black)
    
    # Circle (filled)
    circle_size = 40
    draw.ellipse([
        center_x - circle_size//2, center_y - circle_size//2,
        center_x + circle_size//2, center_y + circle_size//2
    ], fill=black, outline=black)
    
    # Line pattern
    for i in range(5):
        y = center_y + 40 + i * 5
        draw.line([center_x - 80, y, center_x + 80, y], fill=black, width=2)
    
    # Text
    try:
        font = ImageFont.load_default()
        draw.text((center_x - 60, center_y - 60), "BW Test Image", fill=black, font=font)
    except:
        # Fallback if font loading fails
        draw.text((center_x - 60, center_y - 60), "BW Test", fill=black)
    
    # Save the image with _bw suffix to indicate black & white format
    output_path = "data-export/test_bw.png"
    os.makedirs("data-export", exist_ok=True)
    img.save(output_path)
    print(f"Created black & white test image: {output_path}")

    return output_path

if __name__ == "__main__":
    create_bw_test_image()

