// This file is auto-generated by preprocess_files.py. Do not edit this manually.
#pragma once

/*
{meta_data}
Image type: {image_type}
*/

constexpr int {array_name}_width = {cropped_width};
constexpr int {array_name}_height = {cropped_height};
constexpr int {array_name}_x_offset = {min_x};
constexpr int {array_name}_y_offset = {min_y};
constexpr int {array_name}_bits_per_pixel = {bits_per_pixel};
constexpr unsigned char {array_name}_bitmap[] = {{
    {bitmap_data}
}};
