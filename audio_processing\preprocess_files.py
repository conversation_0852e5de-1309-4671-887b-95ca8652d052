import os
from pathlib import Path
from typing import Optional, Any
from .core import (
    process_audio_file,
    clean_destination_folder,
    load_master_template
)


def preprocess_audio_files(source: Optional[Any] = None, target: Optional[Any] = None, env: Optional[Any] = None) -> None:
    """Process MP3 files from data-unprocessed to C++ headers for ESP32 buzzer playback."""
    src_folder = Path("data-unprocessed")
    dst_folder = Path("include/melodies")

    src_folder.mkdir(parents=True, exist_ok=True)
    dst_folder.mkdir(parents=True, exist_ok=True)

    clean_destination_folder(dst_folder, src_folder)
    print(f"Processing MP3 files from {src_folder} to {dst_folder}")
    print("Converting audio to buzzer-compatible melodies with frequency extraction")

    # Collect melody info for master header
    melody_entries = []
    enum_entries = []
    include_entries = []

    # Audio processing parameters - no restrictions
    max_duration = None  # Process entire audio file
    note_duration = 0.1  # Each note lasts 100ms for better resolution
    max_notes = None     # No limit on number of notes

    for file_path in src_folder.glob("*.mp3"):
        file_name = file_path.stem
        
        # Clean up filename for C++ compatibility
        clean_name = (file_name.replace("-", "_")
                               .replace(" ", "_")
                               .replace("#", "")
                               .replace("[", "")
                               .replace("]", "")
                               .replace("(", "")
                               .replace(")", ""))

        output_file = dst_folder / (clean_name + ".h")
        print(f"Processing {file_path} -> {output_file}")

        # Process file and get melody info
        melody_type = process_audio_file(file_path, output_file, max_duration, note_duration, max_notes)
        print()

        if melody_type != "error":
            array_name = clean_name
            melody_entries.append(f'    {{ {array_name}_frequencies, {array_name}_durations, {array_name}_note_count, play_{array_name} }}')
            enum_entries.append(f'    MELODY_{array_name.upper()}')
            include_entries.append(f'#include "{clean_name}.h"')

    if not melody_entries:
        print("No melodies were successfully processed.")
        return

    # Write master header using template
    master_header = dst_folder / "melody_library.h"
    master_template = load_master_template()

    # Debug output
    print(f"DEBUG: Creating master header with {len(include_entries)} includes:")
    for inc in include_entries:
        print(f"  {inc}")

    header_content = master_template.format(
        includes="\n".join(include_entries),
        enum_entries=",\n".join(enum_entries),
        melody_entries=",\n".join(melody_entries)
    )

    # Only overwrite if content changed
    if not master_header.exists() or master_header.read_text(encoding="utf-8") != header_content:
        with open(master_header, "w", encoding="utf-8") as f:
            f.write(header_content)
        print(f"Master header generated: {master_header}")
    else:
        print(f"Master header unchanged: {master_header}")
    print("All MP3 files have been processed and saved as C++ headers.")


# Direct execution support
if __name__ == "__main__":
    preprocess_audio_files()
