#!/usr/bin/env python3
"""
Generate test images for testing the image processing pipeline.
This script creates both black & white and grayscale test images,
and optionally processes them to C++ header files.
"""

import os
import argparse
from pathlib import Path
from PIL import Image, ImageDraw, ImageFont
from typing import Optional
from .core import process_file, clean_destination_folder, load_master_template


def create_bw_test_image(output_dir: Path = None) -> str:
    """Create a test black and white image for testing the new BW image format."""
    if output_dir is None:
        output_dir = Path("data-export")
    
    # Create an image matching the epaper display size
    width, height = 264, 176
    img = Image.new('RGBA', (width, height), (255, 255, 255, 255))  # White background
    draw = ImageDraw.Draw(img)
    
    # Define pure black and white colors
    white = (255, 255, 255, 255)
    black = (0, 0, 0, 255)
    
    # Calculate center positions
    center_x = width // 2
    center_y = height // 2
    
    # Draw some simple black and white shapes centered on screen
    # Rectangle
    rect_size = 40
    draw.rectangle([
        center_x - 60 - rect_size//2, center_y - rect_size//2, 
        center_x - 60 + rect_size//2, center_y + rect_size//2
    ], fill=black, outline=black)
    
    # Circle (filled)
    circle_size = 40
    draw.ellipse([
        center_x - circle_size//2, center_y - circle_size//2,
        center_x + circle_size//2, center_y + circle_size//2
    ], fill=black, outline=black)
    
    # Line pattern
    for i in range(5):
        y = center_y + 40 + i * 5
        draw.line([center_x - 80, y, center_x + 80, y], fill=black, width=2)
    
    # Text
    try:
        font = ImageFont.load_default()
        draw.text((center_x - 60, center_y - 60), "BW Test Image", fill=black, font=font)
    except:
        # Fallback if font loading fails
        draw.text((center_x - 60, center_y - 60), "BW Test", fill=black)
    
    # Save the image with _bw suffix to indicate black & white format
    output_path = output_dir / "test_bw.png"
    output_dir.mkdir(parents=True, exist_ok=True)
    img.save(output_path)
    print(f"Created black & white test image: {output_path}")

    return str(output_path)


def create_grayscale_test_image(output_dir: Path = None) -> str:
    """Create a test image with 4 grayscale levels to demonstrate the epaper display capabilities."""
    if output_dir is None:
        output_dir = Path("data-export")
    
    # Create a 264x176 image (matching the epaper display size)
    width, height = 264, 176
    img = Image.new('RGBA', (width, height), (255, 255, 255, 255))  # White background
    draw = ImageDraw.Draw(img)
    
    # Define the 4 grayscale levels
    white = (255, 255, 255, 255)
    light_gray = (170, 170, 170, 255)  # Will be quantized to 85
    dark_gray = (85, 85, 85, 255)     # Will be quantized to 170
    black = (0, 0, 0, 255)
    
    colors = [white, light_gray, dark_gray, black]
    color_names = ["White", "Light Gray", "Dark Gray", "Black"]
    
    # Draw rectangles for each grayscale level
    rect_width = 60
    rect_height = 40
    start_x = 10
    start_y = 20
    
    for i, (color, name) in enumerate(zip(colors, color_names)):
        x = start_x + i * (rect_width + 5)
        y = start_y
        
        # Draw filled rectangle
        draw.rectangle([x, y, x + rect_width, y + rect_height], fill=color, outline=black, width=2)
        
        # Add label below rectangle
        try:
            # Try to use a default font
            font = ImageFont.load_default()
        except:
            font = None
            
        text_x = x + 5
        text_y = y + rect_height + 5
        draw.text((text_x, text_y), name, fill=black, font=font)
    
    # Add title
    title_text = "4 Grayscale Levels Test"
    try:
        title_font = ImageFont.load_default()
    except:
        title_font = None
    
    draw.text((10, 5), title_text, fill=black, font=title_font)
    
    # Add gradient demonstration
    gradient_start_y = 100
    gradient_height = 30
    gradient_width = width - 20
    
    for x in range(gradient_width):
        # Create a gradient from white to black
        gray_value = int(255 * (1 - x / gradient_width))
        color = (gray_value, gray_value, gray_value, 255)
        draw.line([(10 + x, gradient_start_y), (10 + x, gradient_start_y + gradient_height)], fill=color)
    
    # Add gradient label
    draw.text((10, gradient_start_y + gradient_height + 5), "Gradient (will be quantized to 4 levels)", fill=black, font=font)
    
    # Save the image
    output_path = output_dir / "test_grayscale.png"
    output_dir.mkdir(parents=True, exist_ok=True)
    img.save(output_path)
    print(f"Created test image: {output_path}")
    
    return str(output_path)


def generate_test_images(output_dir: Path = None, process_to_headers: bool = False) -> list[str]:
    """
    Generate both test images and optionally process them to C++ headers.
    
    Args:
        output_dir: Directory to save test images (default: data-export)
        process_to_headers: Whether to also process images to C++ headers
        
    Returns:
        List of created image file paths
    """
    if output_dir is None:
        output_dir = Path("data-export")
    
    print("Generating test images...")
    
    # Generate test images
    created_files = []
    created_files.append(create_bw_test_image(output_dir))
    created_files.append(create_grayscale_test_image(output_dir))
    
    if process_to_headers:
        print("\nProcessing test images to C++ headers...")
        
        # Set up directories
        dst_folder = Path("include/images")
        dst_folder.mkdir(parents=True, exist_ok=True)
        
        # Process each test image
        image_entries = []
        enum_entries = []
        include_entries = []
        
        for image_path in created_files:
            file_path = Path(image_path)
            file_name = file_path.stem
            output_file = dst_folder / (file_name + ".h")
            
            print(f"Processing {file_path} -> {output_file}")
            
            # Process file and get image type info
            image_type, is_bw = process_file(file_path, output_file, enable_dithering=True)
            print()
            
            array_name = file_name.replace("-", "_")
            bits_per_pixel = 1 if is_bw else 2
            image_entries.append(f'    {{ {array_name}_bitmap, {array_name}_width, {array_name}_height, {array_name}_x_offset, {array_name}_y_offset, {bits_per_pixel} }}')
            enum_entries.append(f'    IMAGE_{array_name.upper()}')
            include_entries.append(f'#include "{file_name}.h"')
        
        # Note: Test images are processed to individual headers but not added to master header
        # The master header should only contain production images
        print("Test images processed to individual header files (not added to master header)")
    
    print(f"\nGenerated {len(created_files)} test images:")
    for file_path in created_files:
        print(f"  - {file_path}")
    
    return created_files


def main():
    """Command line interface for test image generation."""
    parser = argparse.ArgumentParser(description="Generate test images for image processing pipeline")
    parser.add_argument("--output-dir", "-o", type=Path, default=Path("data-export"),
                        help="Output directory for test images (default: data-export)")
    parser.add_argument("--process-headers", "-p", action="store_true",
                        help="Also process images to C++ header files")
    parser.add_argument("--bw-only", action="store_true",
                        help="Generate only black & white test image")
    parser.add_argument("--grayscale-only", action="store_true",
                        help="Generate only grayscale test image")
    
    args = parser.parse_args()
    
    if args.bw_only and args.grayscale_only:
        print("Error: Cannot specify both --bw-only and --grayscale-only")
        return 1
    
    # Create output directory
    args.output_dir.mkdir(parents=True, exist_ok=True)
    
    created_files = []
    
    if args.bw_only:
        created_files.append(create_bw_test_image(args.output_dir))
    elif args.grayscale_only:
        created_files.append(create_grayscale_test_image(args.output_dir))
    else:
        created_files = generate_test_images(args.output_dir, args.process_headers)
        return 0
    
    if args.process_headers and created_files:
        print("\nProcessing test images to C++ headers...")
        # Process individual files if not using generate_test_images
        dst_folder = Path("include/images")
        dst_folder.mkdir(parents=True, exist_ok=True)
        
        for image_path in created_files:
            file_path = Path(image_path)
            file_name = file_path.stem
            output_file = dst_folder / (file_name + ".h")
            
            print(f"Processing {file_path} -> {output_file}")
            process_file(file_path, output_file, enable_dithering=True)
    
    print(f"\nGenerated {len(created_files)} test images:")
    for file_path in created_files:
        print(f"  - {file_path}")
    
    return 0


if __name__ == "__main__":
    exit(main())
