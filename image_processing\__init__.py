"""
Image processing module for dosimeter project.

This module contains functionality for:
- Image preprocessing and dithering
- Grayscale and black & white image conversion
- C++ header generation for epaper displays
- Test image generation
"""

from .preprocess_files import preprocess_files
from .create_test_bw import create_bw_test_image
from .create_test_grayscale import create_grayscale_test_image

__all__ = [
    'preprocess_files',
    'create_bw_test_image', 
    'create_grayscale_test_image'
]
