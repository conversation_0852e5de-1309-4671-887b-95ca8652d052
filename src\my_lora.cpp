#include "my_lora.h"
#include "my_io.h"

//Libraries for LoRa
#include <SPI.h>
#include <LoRa.h>

//define the pins used by the LoRa transceiver module
#define SCK 5
#define MISO 19
#define MOSI 27
#define SS 18
#define RST 14
#define DIO0 26

//433E6 for Asia
//866E6 for Europe
//915E6 for North America
#define BAND 866E6

String lora_data;

void onReceive(int packetSize) {
  Serial.println("Packet received");

  if (packetSize) {
    while (LoRa.available()) {
      lora_data = LoRa.readString();
      Serial.println("Data:" + lora_data);
    }
  }
}

void LoRaSetup() {
  //SPI LoRa pins
  SPI.begin(SCK, MISO, MOSI, SS);
  //setup LoRa transceiver module
  LoRa.setPins(SS, RST, DIO0);

  if (!LoRa.begin(BAND)) {
    Serial.println("Starting LoRa failed!");
    error();
  }

  Serial.println("LoRa Initializing OK!");
  Serial.print("LoRa Frequency: ");
  Serial.print(BAND / 1000000);
  Serial.println(" MHz");
}

void LoRaUpdate() {

}
