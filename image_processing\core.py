"""
Core image processing functions shared between preprocessing and test generation.
"""

import os
from pathlib import Path
from PIL import Image
from typing import Optional


def get_script_dir() -> Path:
    """Get the directory of this script, fallback to cwd if __file__ is not defined."""
    try:
        return Path(__file__).parent
    except NameError:
        return Path(os.getcwd())


def get_template_path(template_name: str) -> Path:
    """Get the path to a template file, handling different execution contexts."""
    script_dir = get_script_dir()

    # First try: template in same directory as script (normal case)
    template_path = script_dir / template_name
    if template_path.exists():
        return template_path

    # Second try: template in image_processing subdirectory (if running from root)
    template_path = script_dir / "image_processing" / template_name
    if template_path.exists():
        return template_path

    # Third try: look in current working directory
    template_path = Path(os.getcwd()) / "image_processing" / template_name
    if template_path.exists():
        return template_path

    # Fallback: original path (will cause error if not found)
    return script_dir / template_name


def load_cpp_template() -> str:
    """Load the C++ bitmap template."""
    template_path = get_template_path("cpp_bitmap_template.txt")
    with open(template_path, "r") as f:
        return f.read()


def load_master_template() -> str:
    """Load the C++ master header template."""
    template_path = get_template_path("cpp_master_header_template.txt")
    with open(template_path, "r") as f:
        return f.read()


def crop_and_quantize_image(
    img: Image.Image, enable_dithering: bool = False
) -> tuple[Image.Image, int, int, int, int, int, int, bool]:
    """
    Crop image to visible content and quantize to 4 grayscale levels.
    
    Returns:
        tuple: (cropped_img, min_x, min_y, max_x, max_y, width, height, needs_quantization)
    """
    width, height = img.size
    min_x, min_y = width, height
    max_x, max_y = 0, 0
    for y in range(height):
        for x in range(width):
            r, g, b, a = img.getpixel((x, y))
            if a > 0 and (r, g, b) != (255, 255, 255):  # visible, not white
                min_x = min(min_x, x)
                max_x = max(max_x, x)
                min_y = min(min_y, y)
                max_y = max(max_y, y)
    if min_x > max_x or min_y > max_y:
        return None, min_x, min_y, max_x, max_y, width, height, False
    cropped_img: Image.Image = img.crop((min_x, min_y, max_x + 1, max_y + 1)).convert(
        "L"
    )  # Convert to grayscale
    cropped_width: int
    cropped_height: int
    cropped_width, cropped_height = cropped_img.size

    # Check if quantization is needed (check for 4 grayscale levels: 0, 85, 170, 255)
    unique_values: set[int] = set()
    for y in range(cropped_height):
        for x in range(cropped_width):
            unique_values.add(cropped_img.getpixel((x, y)))
            if len(unique_values) > 4:
                break
        if len(unique_values) > 4:
            break
    needs_quantization: bool = not (unique_values <= {0, 85, 170, 255})

    # Quantize to 4 grayscale levels with optional dithering
    pixels = cropped_img.load()

    def quantize_to_4_levels(value):
        """Quantize a grayscale value to one of 4 levels"""
        if value < 42:  # 0-42 -> 0 (white)
            return 0
        elif value < 127:  # 43-127 -> 85 (light gray)
            return 85
        elif value < 212:  # 128-212 -> 170 (dark gray)
            return 170
        else:  # 213-255 -> 255 (black)
            return 255

    if enable_dithering:
        # Apply Jarvis-Judice-Ninke dithering with serpentine scanning for highest quality
        # JJN is considered one of the best error diffusion algorithms for quality
        # Serpentine scanning reduces artifacts by alternating scan direction each row
        # Matrix:           X   7   5
        #           3   5   7   5   3
        #           1   3   5   3   1
        #                 (1/48)

        # Create error arrays for current and next two rows (JJN needs 2 forward rows)
        error_curr = [0.0] * cropped_width
        error_next1 = [0.0] * cropped_width
        error_next2 = [0.0] * cropped_width

        for y in range(cropped_height):
            # Serpentine scanning: alternate direction each row
            if y % 2 == 0:
                # Left to right (even rows)
                x_range = range(cropped_width)
                x_dir = 1
            else:
                # Right to left (odd rows)
                x_range = range(cropped_width - 1, -1, -1)
                x_dir = -1

            for x in x_range:
                # Get current pixel value and add accumulated error
                old_pixel_float = float(pixels[x, y]) + error_curr[x]
                old_pixel_clamped = max(0.0, min(255.0, old_pixel_float))

                # Quantize to 4 levels
                new_pixel = quantize_to_4_levels(int(old_pixel_clamped))
                pixels[x, y] = new_pixel

                # Calculate quantization error
                quant_error = old_pixel_clamped - float(new_pixel)

                # Distribute error using Jarvis-Judice-Ninke matrix (1/48 total)
                # Adjust directions based on scan direction

                # Current row (forward neighbors in scan direction)
                if 0 <= x + x_dir < cropped_width:
                    error_curr[x + x_dir] += quant_error * (7.0 / 48.0)
                if 0 <= x + 2 * x_dir < cropped_width:
                    error_curr[x + 2 * x_dir] += quant_error * (5.0 / 48.0)

                # Next row (y+1) - adjust for serpentine direction
                if y + 1 < cropped_height:
                    if 0 <= x - 2 * x_dir < cropped_width:
                        error_next1[x - 2 * x_dir] += quant_error * (3.0 / 48.0)
                    if 0 <= x - x_dir < cropped_width:
                        error_next1[x - x_dir] += quant_error * (5.0 / 48.0)
                    error_next1[x] += quant_error * (7.0 / 48.0)
                    if 0 <= x + x_dir < cropped_width:
                        error_next1[x + x_dir] += quant_error * (5.0 / 48.0)
                    if 0 <= x + 2 * x_dir < cropped_width:
                        error_next1[x + 2 * x_dir] += quant_error * (3.0 / 48.0)

                # Row after next (y+2) - adjust for serpentine direction
                if y + 2 < cropped_height:
                    if 0 <= x - 2 * x_dir < cropped_width:
                        error_next2[x - 2 * x_dir] += quant_error * (1.0 / 48.0)
                    if 0 <= x - x_dir < cropped_width:
                        error_next2[x - x_dir] += quant_error * (3.0 / 48.0)
                    error_next2[x] += quant_error * (5.0 / 48.0)
                    if 0 <= x + x_dir < cropped_width:
                        error_next2[x + x_dir] += quant_error * (3.0 / 48.0)
                    if 0 <= x + 2 * x_dir < cropped_width:
                        error_next2[x + 2 * x_dir] += quant_error * (1.0 / 48.0)

            # Shift error arrays for next row
            error_curr = error_next1
            error_next1 = error_next2
            error_next2 = [0.0] * cropped_width
    else:
        # Simple quantization without dithering
        for y in range(cropped_height):
            for x in range(cropped_width):
                old_pixel = pixels[x, y]
                new_pixel = quantize_to_4_levels(old_pixel)
                pixels[x, y] = new_pixel

    return cropped_img, min_x, min_y, max_x, max_y, width, height, needs_quantization


def save_preview_image(cropped_img: Image.Image, output_path: Path):
    """Save a preview image with _preview suffix."""
    preview_path: Path = output_path.with_name(output_path.stem + "_preview.png")
    cropped_img.save(preview_path)


def image_to_grayscale_bytemap(cropped_img: Image.Image) -> list[int]:
    """Convert image to 4-level grayscale bytemap (2 bits per pixel)."""
    cropped_width, cropped_height = cropped_img.size
    pixels = cropped_img.load()
    bytemap: list[int] = []
    for y in range(cropped_height):
        byte = 0
        pixel_count = 0
        for x in range(cropped_width):
            value = pixels[x, y]
            # Map grayscale values to 2-bit values for 4 levels
            # 0 (white) -> 0, 85 (light gray) -> 1, 170 (dark gray) -> 2, 255 (black) -> 3
            if value == 0:
                gray_level = 0
            elif value == 85:
                gray_level = 1
            elif value == 170:
                gray_level = 2
            else:  # value == 255
                gray_level = 3

            byte = (byte << 2) | gray_level  # 2 bits per pixel
            pixel_count += 1
            if pixel_count == 4:  # 4 pixels per byte (2 bits each)
                bytemap.append(byte)
                byte = 0
                pixel_count = 0
        if pixel_count > 0:
            byte = byte << (2 * (4 - pixel_count))  # Pad remaining bits
            bytemap.append(byte)
    return bytemap


def image_to_bw_bytemap(cropped_img: Image.Image) -> list[int]:
    """Convert image to black & white bytemap (1 bit per pixel)."""
    cropped_width, cropped_height = cropped_img.size
    pixels = cropped_img.load()
    bytemap: list[int] = []

    # Convert to pure black and white first
    for y in range(cropped_height):
        for x in range(cropped_width):
            value = pixels[x, y]
            # Threshold at 127 - anything below becomes white (0), above becomes black (255)
            pixels[x, y] = 0 if value < 127 else 255

    # Pack into bytes (8 pixels per byte)
    for y in range(cropped_height):
        byte = 0
        pixel_count = 0
        for x in range(cropped_width):
            value = pixels[x, y]
            # 0 (white) -> 0, 255 (black) -> 1
            bit = 1 if value == 255 else 0
            byte = (byte << 1) | bit
            pixel_count += 1
            if pixel_count == 8:  # 8 pixels per byte
                bytemap.append(byte)
                byte = 0
                pixel_count = 0
        if pixel_count > 0:
            byte = byte << (8 - pixel_count)  # Pad remaining bits
            bytemap.append(byte)
    return bytemap


def bytemap_to_hex(bytemap: list[int]) -> str:
    """Convert bytemap to hex string representation."""
    bitmap_str: str = ""
    for i, b in enumerate(bytemap):
        bitmap_str += f"0x{b:02X}, "
        if (i + 1) % 16 == 0:
            bitmap_str += "\n    "
    return bitmap_str.rstrip()


def process_file(input_path: Path, output_path: Path, enable_dithering: bool = False) -> tuple[str, bool]:
    """
    Process image file and return (image_type, is_bw) tuple.

    Args:
        input_path: Path to input PNG file
        output_path: Path for output header file (without extension)
        enable_dithering: Whether to enable dithering for grayscale images

    Returns:
        tuple: (image_type, is_bw) - image type description and whether it's black & white
    """
    # Determine image type from filename
    is_bw = input_path.stem.endswith('_bw')
    image_type = "black & white" if is_bw else "4-level grayscale"

    # Open image
    img: Image.Image = Image.open(input_path).convert("RGBA")

    if is_bw:
        # For black & white images, skip dithering and quantization
        cropped_img, min_x, min_y, max_x, max_y, width, height, _ = crop_and_quantize_image(
            img, enable_dithering=False
        )
        if cropped_img is None:
            print(f"\t{input_path.name}: No visible pixels found!")
            return image_type, is_bw

        # Convert to pure black and white
        pixels = cropped_img.load()
        cropped_width, cropped_height = cropped_img.size
        for y in range(cropped_height):
            for x in range(cropped_width):
                value = pixels[x, y]
                pixels[x, y] = 0 if value < 127 else 255

        bytemap = image_to_bw_bytemap(cropped_img)
        print(f"\t{input_path.name}: Converted to black & white.")
    else:
        # For grayscale images, use existing logic
        cropped_img, min_x, min_y, max_x, max_y, width, height, needs_quantization = crop_and_quantize_image(
            img, enable_dithering
        )
        if cropped_img is None:
            print(f"\t{input_path.name}: No visible pixels found!")
            return image_type, is_bw

        if needs_quantization:
            dither_msg = " with dithering" if enable_dithering else ""
            print(f"\t{input_path.name}: Quantized to 4 grayscale levels{dither_msg}.")
        else:
            print(f"\t{input_path.name}: Already in 4 grayscale levels.")

        bytemap = image_to_grayscale_bytemap(cropped_img)

    cropped_width, cropped_height = cropped_img.size
    save_preview_image(cropped_img, output_path)
    bitmap_str = bytemap_to_hex(bytemap)
    compression_ratio: float = (cropped_width * cropped_height) / (width * height)

    # Write C++ header file using template
    array_name: str = output_path.stem.replace("-", "_")
    cpp_template: str = load_cpp_template()
    header_content: str = cpp_template.format(
        array_name=array_name,
        cropped_width=cropped_width,
        cropped_height=cropped_height,
        min_x=min_x,
        min_y=min_y,
        bitmap_data=bitmap_str,
        meta_data=f"Compressed {input_path.name} ({image_type}) with ratio {compression_ratio:.2f}",
        image_type=image_type,
        bits_per_pixel=1 if is_bw else 2,
    )
    with open(output_path.with_suffix(".h"), "w") as f:
        f.write(header_content)

    print(
        f"\t{input_path.name}: cropped to ({min_x},{min_y}) size {cropped_width}x{cropped_height}, exported as {output_path.with_suffix('.h').name}"
    )

    return image_type, is_bw


def clean_destination_folder(dst_folder: Path, src_folder: Path):
    """Clean up destination folder and preview images."""
    print(f"Cleaning up destination folder: {dst_folder}")

    # Clean up header files with error handling
    for header_file in dst_folder.glob("*.h"):
        try:
            header_file.unlink()
            print(f"Removed: {header_file}")
        except PermissionError:
            print(f"Warning: Could not remove {header_file} (permission denied)")
        except Exception as e:
            print(f"Warning: Could not remove {header_file}: {e}")

    # Only delete preview images, not source PNG files
    for preview_file in src_folder.glob("*_preview.png"):
        try:
            preview_file.unlink()
            print(f"Removed: {preview_file}")
        except PermissionError:
            print(f"Warning: Could not remove {preview_file} (permission denied)")
        except Exception as e:
            print(f"Warning: Could not remove {preview_file}: {e}")

    print("Destination folder cleaned successfully")
