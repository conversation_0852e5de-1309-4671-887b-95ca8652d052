// This file is auto-generated by preprocess_files.py. Do not edit this manually.
#pragma once

#include <GxEPD2_4G_4G.h>

{includes}

#ifdef __cplusplus
extern "C" {{
#endif

typedef struct {{
    const unsigned char* bitmap;
    int width;
    int height;
    int x_offset;
    int y_offset;
    int bits_per_pixel;
}} ImageBitmap;

typedef enum {{
{enum_entries}
}} ImageId;

static const ImageBitmap imageTable[] = {{
{image_entries}
}};

static inline const ImageBitmap* getImage(ImageId id) {{ return &imageTable[id]; }}

#ifdef __cplusplus
}}

// C++ Image Classes
#ifdef __cplusplus

class BaseImage {{
protected:
    const ImageBitmap* bitmap_data;

public:
    BaseImage(ImageId id) : bitmap_data(getImage(id)) {{}}

    int width() const {{ return bitmap_data->width; }}
    int height() const {{ return bitmap_data->height; }}
    int x_offset() const {{ return bitmap_data->x_offset; }}
    int y_offset() const {{ return bitmap_data->y_offset; }}
    int bits_per_pixel() const {{ return bitmap_data->bits_per_pixel; }}
    const unsigned char* bitmap() const {{ return bitmap_data->bitmap; }}

    // Auto-detect drawing method based on bits per pixel
    template<typename DisplayType>
    void draw(DisplayType& display, int x, int y) {{
        if (bitmap_data->bits_per_pixel == 1) {{
            // Black & white image - use drawBitmap
            display.drawBitmap(
                x + bitmap_data->x_offset,
                y + bitmap_data->y_offset,
                bitmap_data->bitmap,
                bitmap_data->width,
                bitmap_data->height,
                GxEPD_BLACK
            );
        }} else if (bitmap_data->bits_per_pixel == 2) {{
            // Grayscale image - use drawGreyPixmap
            display.drawGreyPixmap(
                bitmap_data->bitmap,
                2, // 2 bits per pixel for 4 grayscale levels
                x + bitmap_data->x_offset,
                y + bitmap_data->y_offset,
                bitmap_data->width,
                bitmap_data->height
            );
        }} else {{
            // Unsupported format - could add error handling here
            // For now, fall back to black & white
            display.drawBitmap(
                x + bitmap_data->x_offset,
                y + bitmap_data->y_offset,
                bitmap_data->bitmap,
                bitmap_data->width,
                bitmap_data->height,
                GxEPD_BLACK
            );
        }}
    }}
}};



#endif // __cplusplus
#endif
