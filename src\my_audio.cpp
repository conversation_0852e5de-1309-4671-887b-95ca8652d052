#include "my_audio.h"
#include "my_io.h"
#include <Arduino.h>

void playRandomMelody() {
    MelodyId randomId = getRandomMelodyId();
    Serial.printf("Playing random melody ID: %d\n", randomId);
    playMelody(randomId);
}

void playMelodyById(MelodyId melody_id) {
    Serial.printf("Playing melody ID: %d\n", melody_id);
    playMelody(melody_id);
}

void testMelodies() {
    Serial.println("Testing all available melodies...");
    int count = getMelodyCount();
    
    for (int i = 0; i < count; i++) {
        Serial.printf("Testing melody %d/%d\n", i + 1, count);
        playMelodyById((MelodyId)i);
        delay(1000); // Pause between melodies
    }
    
    Serial.println("Melody test completed.");
}

int getMelodyCount() {
    return sizeof(melodies) / sizeof(melodies[0]);
}

MelodyId getRandomMelodyId() {
    int count = getMelodyCount();
    if (count == 0) return (MelodyId)0;
    
    // Use millis() as a simple random seed
    int randomIndex = millis() % count;
    return (MelodyId)randomIndex;
}
