// This file is auto-generated by preprocess_audio_files.py. Do not edit this manually.
#pragma once

// Include all melody headers
#include "Reloaded_installer_1_4Yn174EAEwk.h"

// Melody enumeration
enum MelodyId {
    MELODY_RELOADED_INSTALLER_1_4YN174EAEWK
};

// Melody info structure
struct MelodyInfo {
    const int* frequencies;
    const int* durations;
    int note_count;
    void (*play_function)();
};

// Array of all melodies
constexpr MelodyInfo melodies[] = {
    { Reloaded_installer_1_4Yn174EAEwk_frequencies, Reloaded_installer_1_4Yn174EAEwk_durations, Reloaded_installer_1_4Yn174EAEwk_note_count, play_Reloaded_installer_1_4Yn174EAEwk }
};

// Helper function to play a melody by ID
inline void playMelody(MelodyId melody_id) {
    if (melody_id >= 0 && melody_id < sizeof(melodies) / sizeof(melodies[0])) {
        melodies[melody_id].play_function();
    }
}

// Helper function to get melody info
inline const MelodyInfo& getMelodyInfo(MelodyId melody_id) {
    static const MelodyInfo empty_melody = {nullptr, nullptr, 0, nullptr};
    if (melody_id >= 0 && melody_id < sizeof(melodies) / sizeof(melodies[0])) {
        return melodies[melody_id];
    }
    return empty_melody;
}
