void IOSetup();
void IOEnd();

float batteryVoltage();
void error();

void beep(int frequency = 1000, int duration = 500);
void testBeep();

void setupEncoderRotationWakeup();

// Encoder reading functions
bool isEncoderButtonPressed();
int readEncoderRotation(); // Returns -1, 0, or 1 for rotation direction

// Boot count functions
int getBootCount();

// Encoder input handling
void handleEncoderInput();

inline bool batteryEmpty(float voltage) {
  return voltage <= 3.2;
}
inline bool batteryLow(float voltage) {
  return voltage <= 3.6;
}
inline bool batteryMedium(float voltage) {
  return voltage <= 3.9;
}
inline bool batteryHigh(float voltage) {
  return voltage <= 4.1;
}
inline bool batteryFull(float voltage) {
  return voltage >= 4.1;
}