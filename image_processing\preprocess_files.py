import os
from pathlib import Path
from typing import Optional, Any
from .core import (
    process_file,
    clean_destination_folder,
    load_master_template
)


def preprocess_files(source: Optional[Any] = None, target: Optional[Any] = None, env: Optional[Any] = None) -> None:
    """Process PNG files from data-export to C++ headers with dithering enabled by default."""
    src_folder = Path("data-export")
    dst_folder = Path("include/images")

    src_folder.mkdir(parents=True, exist_ok=True)
    dst_folder.mkdir(parents=True, exist_ok=True)

    clean_destination_folder(dst_folder, src_folder)
    print(f"Processing PNG files from {src_folder} to {dst_folder}")
    print("High-quality Jarvis-Judice-Ninke dithering with serpentine scanning enabled for best image detail (grayscale images only)")

    # Collect image info for master header
    image_entries = []
    enum_entries = []
    include_entries = []

    for file_path in src_folder.glob("*.png"):
        file_name = file_path.stem

        # Skip test images - they should be processed separately
        if file_name.startswith("test_"):
            print(f"Skipping test image: {file_path.name} (use generate_test_images script for test images)")
            continue

        output_file = dst_folder / (file_name + ".h")
        print(f"Processing {file_path} -> {output_file}")

        # Process file and get image type info
        image_type, is_bw = process_file(file_path, output_file, enable_dithering=True)
        print()

        array_name = file_name.replace("-", "_")
        bits_per_pixel = 1 if is_bw else 2
        image_entries.append(f'    {{ {array_name}_bitmap, {array_name}_width, {array_name}_height, {array_name}_x_offset, {array_name}_y_offset, {bits_per_pixel} }}')
        enum_entries.append(f'    IMAGE_{array_name.upper()}')
        include_entries.append(f'#include "{file_name}.h"')

    # Write master header using template
    master_header = dst_folder / "image_bitmaps.h"
    master_template = load_master_template()

    # Debug output
    print(f"DEBUG: Creating master header with {len(include_entries)} includes:")
    for inc in include_entries:
        print(f"  {inc}")

    header_content = master_template.format(
        includes="\n".join(include_entries),
        enum_entries=",\n".join(enum_entries),
        image_entries=",\n".join(image_entries)
    )

    # Only overwrite if content changed
    if not master_header.exists() or master_header.read_text(encoding="utf-8") != header_content:
        with open(master_header, "w", encoding="utf-8") as f:
            f.write(header_content)
        print(f"Master header generated: {master_header}")
    else:
        print(f"Master header unchanged: {master_header}")
    print("All PNG files have been processed and saved as C++ headers.")


# Direct execution support
if __name__ == "__main__":
    preprocess_files()
