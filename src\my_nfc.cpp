#include "my_nfc.h"

#include <Wire.h>
#include <SmeNfc.h>

// NT3H1101W0FFT

#define NFC_SCL 15
#define NFC_SDA 4

SmeNfc nfc;  // Create NFC object

bool nfcOk;
byte buffer[UID_SIZE];

void NFCSetup() {
  Wire.begin(NFC_SDA, NFC_SCL);

  // just clear the buffer
  for (int i = 0; i < UID_SIZE; i++) {
    buffer[i] = 0;
  }

  if (smeNfcDriver.readUID(buffer)) {
    smeNfc.storeText(NDEFFirstPos, "TEST");
    nfcOk = true;
  }
}

void NFCUpdate() {
  if (nfcOk == true) {
    // Send the UID of the NT3H1101 to the Console
    Serial.print("Serial number (UID): ");
    for (int i = 0; i < UID_SIZE; i++) {
      Serial.print(buffer[i], HEX);
      Serial.print(':');
    }
    Serial.println();
  }
}