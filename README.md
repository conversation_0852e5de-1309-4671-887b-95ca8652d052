# ESP32 Dosimeter Project

A comprehensive ESP32-based dosimeter with display, audio, and sensor capabilities.

## Features

- **E-Paper Display**: High-contrast display for readings and status
- **OLED Display**: Secondary display for real-time information
- **Audio System**: Buzzer-based melody playback from MP3 sources
- **Input Controls**: Rotary encoder with button for navigation
- **Wireless**: LoRa communication capabilities
- **NFC**: Near-field communication support
- **Power Management**: Battery monitoring and sleep modes

## Audio Processing

The project includes a sophisticated audio processing system that converts MP3 files to ESP32 buzzer-compatible melodies.

### Quick Start

1. Place MP3 files in `data-unprocessed/` folder
2. Run the audio processor:
   ```bash
   python process_audio.py
   ```
3. Include in your ESP32 code:
   ```cpp
   #include "my_audio.h"

   void setup() {
       IOSetup();
   }

   void loop() {
       playRandomMelody();
       delay(5000);
   }
   ```

See [AUDIO_PROCESSING.md](AUDIO_PROCESSING.md) for detailed documentation.

## Image Processing

The project also includes image processing for converting PNG files to display-compatible formats.

See [IMAGE_FORMATS.md](IMAGE_FORMATS.md) for detailed documentation.

## Build System

The project uses PlatformIO with custom build scripts that automatically process assets:

```bash
# Build the project (auto-processes images and audio)
pio run

# Manually process images
pio run -t preprocess_files

# Manually process audio
pio run -t preprocess_audio_files

# Clean up generated files
pio run -t cleanup_images
```

## Hardware Configuration

- **Board**: TTGO LoRa32 V1
- **Buzzer**: Pin 12
- **Encoder**: Pins 13 (CLK), 21 (DATA), 17 (BUTTON)
- **Battery**: Pin 34 (voltage monitoring)
- **LED**: Pin 0 (WS2812)

## Dependencies

### Python Dependencies
- `librosa` - Audio analysis
- `soundfile` - Audio file I/O
- `pillow` - Image processing
- `matplotlib` - Visualization (required by librosa)
- `platformio` - Build system

### ESP32 Libraries
- U8g2 - Display graphics
- LoRa - Wireless communication
- GxEPD2 - E-paper display driver
- ESPAsyncWebServer - Web interface

## Project Structure

```
├── src/                    # ESP32 source code
├── include/                # Header files
│   ├── images/            # Generated image headers
│   └── melodies/          # Generated melody headers
├── data-unprocessed/      # Source MP3 and image files
├── data-export/           # Processed image files
├── image_processing/      # Image processing scripts
├── audio_processing/      # Audio processing scripts
└── docs/                  # Documentation
```