#!/usr/bin/env python3
"""
Create a test image with 4 grayscale levels to demonstrate the epaper display capabilities.
"""

from PIL import Image, ImageDraw, ImageFont
import os

def create_grayscale_test_image():
    # Create a 264x176 image (matching the epaper display size)
    width, height = 264, 176
    img = Image.new('RGBA', (width, height), (255, 255, 255, 255))  # White background
    draw = ImageDraw.Draw(img)
    
    # Define the 4 grayscale levels
    white = (255, 255, 255, 255)
    light_gray = (170, 170, 170, 255)  # Will be quantized to 85
    dark_gray = (85, 85, 85, 255)     # Will be quantized to 170
    black = (0, 0, 0, 255)
    
    colors = [white, light_gray, dark_gray, black]
    color_names = ["White", "Light Gray", "Dark Gray", "Black"]
    
    # Draw rectangles for each grayscale level
    rect_width = 60
    rect_height = 40
    start_x = 10
    start_y = 20
    
    for i, (color, name) in enumerate(zip(colors, color_names)):
        x = start_x + i * (rect_width + 5)
        y = start_y
        
        # Draw filled rectangle
        draw.rectangle([x, y, x + rect_width, y + rect_height], fill=color, outline=black, width=2)
        
        # Add label below rectangle
        try:
            # Try to use a default font
            font = ImageFont.load_default()
        except:
            font = None
            
        text_x = x + 5
        text_y = y + rect_height + 5
        draw.text((text_x, text_y), name, fill=black, font=font)
    
    # Add title
    title_text = "4 Grayscale Levels Test"
    try:
        title_font = ImageFont.load_default()
    except:
        title_font = None
    
    draw.text((10, 5), title_text, fill=black, font=title_font)
    
    # Add gradient demonstration
    gradient_start_y = 100
    gradient_height = 30
    gradient_width = width - 20
    
    for x in range(gradient_width):
        # Create a gradient from white to black
        gray_value = int(255 * (1 - x / gradient_width))
        color = (gray_value, gray_value, gray_value, 255)
        draw.line([(10 + x, gradient_start_y), (10 + x, gradient_start_y + gradient_height)], fill=color)
    
    # Add gradient label
    draw.text((10, gradient_start_y + gradient_height + 5), "Gradient (will be quantized to 4 levels)", fill=black, font=font)
    
    # Save the image
    output_path = "data-export/test_grayscale.png"
    os.makedirs("data-export", exist_ok=True)
    img.save(output_path)
    print(f"Created test image: {output_path}")
    
    return output_path

if __name__ == "__main__":
    create_grayscale_test_image()
